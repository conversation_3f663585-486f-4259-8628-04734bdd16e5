{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exploratory Data Analysis"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os, json, re"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["df = pd.read_json('./data/all_races.json')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>actual_wt</th>\n", "      <th>date</th>\n", "      <th>declared_wt</th>\n", "      <th>draw</th>\n", "      <th>finish_time</th>\n", "      <th>going</th>\n", "      <th>horse_id</th>\n", "      <th>horse_name</th>\n", "      <th>horse_no</th>\n", "      <th>jockey_name</th>\n", "      <th>lbw</th>\n", "      <th>plc</th>\n", "      <th>public_odds</th>\n", "      <th>race_dist</th>\n", "      <th>race_id</th>\n", "      <th>race_no</th>\n", "      <th>race_type</th>\n", "      <th>running_pos</th>\n", "      <th>trainer_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>120</td>\n", "      <td>2019-12-29</td>\n", "      <td>1066</td>\n", "      <td>5</td>\n", "      <td>1:22.73</td>\n", "      <td>GOOD</td>\n", "      <td>C413</td>\n", "      <td>RICH_AND_LUCKY</td>\n", "      <td>2.0</td>\n", "      <td><PERSON></td>\n", "      <td>-</td>\n", "      <td>1</td>\n", "      <td>1.7</td>\n", "      <td>1400</td>\n", "      <td>284</td>\n", "      <td>RACE_1</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>3 4 3 1</td>\n", "      <td>L Ho</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>120</td>\n", "      <td>2019-12-29</td>\n", "      <td>1078</td>\n", "      <td>4</td>\n", "      <td>1:22.87</td>\n", "      <td>GOOD</td>\n", "      <td>C437</td>\n", "      <td>KING_DRAGON</td>\n", "      <td>7.0</td>\n", "      <td>S de Sousa</td>\n", "      <td>3/4</td>\n", "      <td>2</td>\n", "      <td>11</td>\n", "      <td>1400</td>\n", "      <td>284</td>\n", "      <td>RACE_1</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>2 2 1 2</td>\n", "      <td><PERSON> L Man</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>120</td>\n", "      <td>2019-12-29</td>\n", "      <td>1297</td>\n", "      <td>1</td>\n", "      <td>1:22.97</td>\n", "      <td>GOOD</td>\n", "      <td>D091</td>\n", "      <td>INVALUABLE</td>\n", "      <td>6.0</td>\n", "      <td><PERSON></td>\n", "      <td>1-1/2</td>\n", "      <td>3</td>\n", "      <td>6.3</td>\n", "      <td>1400</td>\n", "      <td>284</td>\n", "      <td>RACE_1</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>6 6 7 3</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>120</td>\n", "      <td>2019-12-29</td>\n", "      <td>1087</td>\n", "      <td>3</td>\n", "      <td>1:23.24</td>\n", "      <td>GOOD</td>\n", "      <td>C419</td>\n", "      <td>KELMIMI_WINS</td>\n", "      <td>8.0</td>\n", "      <td>A <PERSON>el</td>\n", "      <td>3-1/4</td>\n", "      <td>4</td>\n", "      <td>26</td>\n", "      <td>1400</td>\n", "      <td>284</td>\n", "      <td>RACE_1</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>5 7 5 4</td>\n", "      <td>C H Yip</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>120</td>\n", "      <td>2019-12-29</td>\n", "      <td>1022</td>\n", "      <td>2</td>\n", "      <td>1:23.51</td>\n", "      <td>GOOD</td>\n", "      <td>C342</td>\n", "      <td>VICTORY_FOR_ALL</td>\n", "      <td>5.0</td>\n", "      <td>T H So</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>147</td>\n", "      <td>1400</td>\n", "      <td>284</td>\n", "      <td>RACE_1</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>4 3 4 5</td>\n", "      <td><PERSON> <PERSON> Tsui</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Unnamed: 0  actual_wt       date declared_wt draw finish_time going  \\\n", "0           0        120 2019-12-29        1066    5     1:22.73  GOOD   \n", "1           1        120 2019-12-29        1078    4     1:22.87  GOOD   \n", "2           2        120 2019-12-29        1297    1     1:22.97  GOOD   \n", "3           3        120 2019-12-29        1087    3     1:23.24  GOOD   \n", "4           4        120 2019-12-29        1022    2     1:23.51  GOOD   \n", "\n", "  horse_id       horse_name  horse_no  jockey_name    lbw plc public_odds  \\\n", "0     C413   RICH_AND_LUCKY       2.0     Z Purton      -   1         1.7   \n", "1     C437      KING_DRAGON       7.0   S de Sousa    3/4   2          11   \n", "2     D091       INVALUABLE       6.0  C Schofield  1-1/2   3         6.3   \n", "3     C419     KELMIMI_WINS       8.0      A Badel  3-1/4   4          26   \n", "4     C342  VICTORY_FOR_ALL       5.0       T H So      5   5         147   \n", "\n", "   race_dist  race_id race_no        race_type running_pos trainer_name  \n", "0       1400      284  RACE_1  RESTRICTED_RACE     3 4 3 1         L Ho  \n", "1       1400      284  RACE_1  RESTRICTED_RACE     2 2 1 2      K L Man  \n", "2       1400      284  RACE_1  RESTRICTED_RACE     6 6 7 3     R Gibson  \n", "3       1400      284  RACE_1  RESTRICTED_RACE     5 7 5 4      C H Yip  \n", "4       1400      284  RACE_1  RESTRICTED_RACE     4 3 4 5     Y S Tsui  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 2}