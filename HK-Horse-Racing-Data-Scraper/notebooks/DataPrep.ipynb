{"cells": [{"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import glob\n", "import csv,json, re,os"]}, {"cell_type": "code", "execution_count": 185, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.7/site-packages/ipykernel_launcher.py:35: FutureWarning: Sorting because non-concatenation axis is not aligned. A future version\n", "of pandas will change to not sort by default.\n", "\n", "To accept the future behavior, pass 'sort=False'.\n", "\n", "To retain the current behavior and silence the warning, pass 'sort=True'.\n", "\n"]}], "source": ["# Sort race files\n", "def key_func(afilename):\n", "    nondigits = re.compile(\"\\D\")\n", "    return int(nondigits.sub(\"\", afilename))\n", "\n", "# Store dfs\n", "df_list = []\n", "\n", "# Dates\n", "dates = [\"29/12/2019\", \"26/12/2019\", \"21/12/2019\", \"18/12/2019\", \"15/12/2019\", \"11/12/2019\", \"08/12/2019\",\n", " \"04/12/2019\", \"01/12/2019\", \"27/11/2019\", \"23/11/2019\", \"20/11/2019\", \"17/11/2019\", \"09/11/2019\",\n", "  \"06/11/2019\", \"03/11/2019\", \"30/10/2019\", \"27/10/2019\", \"23/10/2019\", \"20/10/2019\", \"16/10/2019\",\n", "   \"12/10/2019\", \"09/10/2019\", \"01/10/2019\", \"25/09/2019\", \"21/09/2019\", \"15/09/2019\", \"11/09/2019\",\n", "     \"01/09/2019\", \"14/07/2019\", \"10/07/2019\", \"07/07/2019\", \"03/07/2019\", \"01/07/2019\", \"26/06/2019\", \"23/06/2019\",\n", "      \"16/06/2019\", \"05/06/2019\", \"29/05/2019\", \"22/05/2019\", \"15/05/2019\", \"11/05/2019\", \"08/05/2019\", \"05/05/2019\",\n", "       \"01/05/2019\", \"28/04/2019\", \"22/04/2019\",  \"07/04/2019\",\"03/04/2019\", \"31/03/2019\", \"24/03/2019\", \"23/03/2019\", \"20/03/2019\",\n", "        \"17/03/2019\", \"13/03/2019\", \"10/03/2019\", \"06/03/2019\", \"02/03/2019\", \"27/02/2019\",\n", "         \"24/02/2019\", \"17/02/2019\", \"13/02/2019\",\"10/02/2019\", \"07/02/2019\", \"02/02/2019\", \"30/01/2019\", \"27/01/2019\",\n", "          \"23/01/2019\", \"20/01/2019\", \"16/01/2019\", \"12/01/2019\", \"09/01/2019\", \"06/01/2019\", \"01/01/2019\",\n", "           \"29/12/2018\", \"26/12/2018\", \"23/12/2018\", \"19/12/2018\", \"16/12/2018\", \"12/12/2018\", \"09/12/2018\",\n", "            \"05/12/2018\", \"02/12/2018\", \"28/11/2018\", \"25/11/2018\", \"21/11/2018\", \"18/11/2018\", \"14/11/2018\", \"10/11/2018\", \"07/11/2018\",\n", "             \"04/11/2018\", \"31/10/2018\", \"28/10/2018\", \"24/10/2018\", \"21/10/2018\",  \"18/10/2018\",\n", "              \"13/10/2018\", \"10/10/2018\", \"01/10/2018\", \"26/09/2018\", \"22/09/2018\", \"12/09/2018\",\n", "               \"09/09/2018\", \"05/09/2018\", \"02/09/2018\"]\n", "\n", "count = 0\n", "\n", "for file in sorted(glob.glob('./data/races*.csv'), key=key_func):\n", "    temp_df = (pd.read_csv(file))\n", "    temp_df[\"date\"] = dates[count]\n", "    count+=1\n", "    df_list.append(temp_df)\n", "\n", "# Return df\n", "df = pd.concat(df_list)"]}, {"cell_type": "code", "execution_count": 186, "metadata": {}, "outputs": [], "source": ["# Rename and reorder columns\n", "cols = df.columns.tolist()\n", "cols = [str(y) for y in sorted([int(x) for x in cols[:-1]])]\n", "cols.append('date')\n", "df = df[cols]\n", "df.columns = ['race_no', 'going', 'race_type', 'plc', 'horse_no', 'horse_name', 'jockey_name', 'trainer_name',\n", "              'actual_wt', 'declared_wt', 'draw', 'lbw', 'running_pos', 'finish_time', 'public_odds', 'date']"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [], "source": ["# Clean headers\n", "df[\"race_id\"] = [x.split('(')[1][:-1] for x in df.race_no]\n", "df[\"race_no\"] = [re.sub(' ', '_',x.split('(')[0][:-1]) for x in df.race_no]\n", "df[\"race_dist\"] = [x.split(\"-\")[1].strip().replace(' ','_').upper()[:-1] for x in df.race_type]\n", "df[\"race_type\"] = [x.split(\"-\")[0].strip().replace(' ','_').upper() for x in df.race_type]\n", "df[\"horse_id\"] = [x.split(\"(\")[1].strip()[:-1] for x in df.horse_name]\n", "df[\"horse_name\"] = [x.split(\"(\")[0].strip().replace(\" \", \"_\") for x in df.horse_name]"]}, {"cell_type": "code", "execution_count": 190, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>race_no</th>\n", "      <th>going</th>\n", "      <th>race_type</th>\n", "      <th>plc</th>\n", "      <th>horse_no</th>\n", "      <th>horse_name</th>\n", "      <th>jockey_name</th>\n", "      <th>trainer_name</th>\n", "      <th>actual_wt</th>\n", "      <th>declared_wt</th>\n", "      <th>draw</th>\n", "      <th>lbw</th>\n", "      <th>running_pos</th>\n", "      <th>finish_time</th>\n", "      <th>public_odds</th>\n", "      <th>date</th>\n", "      <th>race_id</th>\n", "      <th>race_dist</th>\n", "      <th>horse_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>RACE_1</td>\n", "      <td>GOOD</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "      <td>RICH_AND_LUCKY</td>\n", "      <td><PERSON></td>\n", "      <td>L Ho</td>\n", "      <td>120</td>\n", "      <td>1066</td>\n", "      <td>5</td>\n", "      <td>-</td>\n", "      <td>3 4 3 1</td>\n", "      <td>1:22.73</td>\n", "      <td>1.7</td>\n", "      <td>29/12/2019</td>\n", "      <td>284</td>\n", "      <td>1400</td>\n", "      <td>C413</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>RACE_1</td>\n", "      <td>GOOD</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>2</td>\n", "      <td>7.0</td>\n", "      <td>KING_DRAGON</td>\n", "      <td>S de Sousa</td>\n", "      <td><PERSON> L Man</td>\n", "      <td>120</td>\n", "      <td>1078</td>\n", "      <td>4</td>\n", "      <td>3/4</td>\n", "      <td>2 2 1 2</td>\n", "      <td>1:22.87</td>\n", "      <td>11</td>\n", "      <td>29/12/2019</td>\n", "      <td>284</td>\n", "      <td>1400</td>\n", "      <td>C437</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>RACE_1</td>\n", "      <td>GOOD</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>3</td>\n", "      <td>6.0</td>\n", "      <td>INVALUABLE</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>120</td>\n", "      <td>1297</td>\n", "      <td>1</td>\n", "      <td>1-1/2</td>\n", "      <td>6 6 7 3</td>\n", "      <td>1:22.97</td>\n", "      <td>6.3</td>\n", "      <td>29/12/2019</td>\n", "      <td>284</td>\n", "      <td>1400</td>\n", "      <td>D091</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>RACE_1</td>\n", "      <td>GOOD</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>4</td>\n", "      <td>8.0</td>\n", "      <td>KELMIMI_WINS</td>\n", "      <td>A <PERSON>el</td>\n", "      <td>C H Yip</td>\n", "      <td>120</td>\n", "      <td>1087</td>\n", "      <td>3</td>\n", "      <td>3-1/4</td>\n", "      <td>5 7 5 4</td>\n", "      <td>1:23.24</td>\n", "      <td>26</td>\n", "      <td>29/12/2019</td>\n", "      <td>284</td>\n", "      <td>1400</td>\n", "      <td>C419</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>RACE_1</td>\n", "      <td>GOOD</td>\n", "      <td>RESTRICTED_RACE</td>\n", "      <td>5</td>\n", "      <td>5.0</td>\n", "      <td>VICTORY_FOR_ALL</td>\n", "      <td>T H So</td>\n", "      <td><PERSON> <PERSON> Tsui</td>\n", "      <td>120</td>\n", "      <td>1022</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>4 3 4 5</td>\n", "      <td>1:23.51</td>\n", "      <td>147</td>\n", "      <td>29/12/2019</td>\n", "      <td>284</td>\n", "      <td>1400</td>\n", "      <td>C342</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  race_no going        race_type plc  horse_no       horse_name  jockey_name  \\\n", "0  RACE_1  GOOD  RESTRICTED_RACE   1       2.0   RICH_AND_LUCKY     Z Purton   \n", "1  RACE_1  GOOD  RESTRICTED_RACE   2       7.0      KING_DRAGON   S de Sousa   \n", "2  RACE_1  GOOD  RESTRICTED_RACE   3       6.0       INVALUABLE  C Schofield   \n", "3  RACE_1  GOOD  RESTRICTED_RACE   4       8.0     KELMIMI_WINS      A Badel   \n", "4  RACE_1  GOOD  RESTRICTED_RACE   5       5.0  VICTORY_FOR_ALL       T H So   \n", "\n", "  trainer_name  actual_wt declared_wt draw    lbw running_pos finish_time  \\\n", "0         L Ho        120        1066    5      -     3 4 3 1     1:22.73   \n", "1      K L Man        120        1078    4    3/4     2 2 1 2     1:22.87   \n", "2     <PERSON>        120        1297    1  1-1/2     6 6 7 3     1:22.97   \n", "3      C H Yi<PERSON>        120        1087    3  3-1/4     5 7 5 4     1:23.24   \n", "4     Y S Tsui        120        1022    2      5     4 3 4 5     1:23.51   \n", "\n", "  public_odds        date race_id race_dist horse_id  \n", "0         1.7  29/12/2019     284      1400     C413  \n", "1          11  29/12/2019     284      1400     C437  \n", "2         6.3  29/12/2019     284      1400     D091  \n", "3          26  29/12/2019     284      1400     C419  \n", "4         147  29/12/2019     284      1400     C342  "]}, "execution_count": 190, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.5"}}, "nbformat": 4, "nbformat_minor": 2}