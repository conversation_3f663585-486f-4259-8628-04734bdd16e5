#!/usr/bin/env python3
"""
Enhanced Benter System Project Setup Script
Creates the complete directory structure for the horse racing system
"""

import os
import sys
from pathlib import Path

def create_directory_structure():
    """Create the complete project directory structure"""
    
    # Base project structure
    directories = [
        # Core system directories
        "src",
        "src/benter_system",
        "src/benter_system/core",
        "src/benter_system/models",
        "src/benter_system/features",
        "src/benter_system/betting",
        "src/benter_system/risk",
        "src/benter_system/backtesting",
        
        # Data collection and processing
        "src/data_collection",
        "src/data_collection/scrapers",
        "src/data_collection/apis",
        "src/data_collection/validators",
        "src/data_collection/orchestration",
        
        # Database and ETL
        "src/database",
        "src/database/models",
        "src/database/migrations",
        "src/database/etl",
        
        # OCTAVE Integration
        "src/octave",
        "src/octave/compression",
        "src/octave/documentation",
        "src/octave/artifacts",
        
        # Semantic Immune System
        "src/semantic_immune",
        "src/semantic_immune/archetypes",
        "src/semantic_immune/antibodies",
        "src/semantic_immune/validators",
        
        # Configuration and utilities
        "config",
        "config/environments",
        "config/models",
        "config/features",
        
        # Data directories
        "data",
        "data/raw",
        "data/processed",
        "data/features",
        "data/models",
        "data/backups",
        
        # Notebooks and analysis
        "notebooks",
        "notebooks/eda",
        "notebooks/modeling",
        "notebooks/backtesting",
        "notebooks/research",
        
        # Tests
        "tests",
        "tests/unit",
        "tests/integration",
        "tests/data",
        
        # Documentation
        "docs",
        "docs/api",
        "docs/user_guide",
        "docs/development",
        "docs/octave_artifacts",
        
        # Deployment and infrastructure
        "deployment",
        "deployment/docker",
        "deployment/kubernetes",
        "deployment/terraform",
        
        # Monitoring and logging
        "monitoring",
        "monitoring/grafana",
        "monitoring/prometheus",
        
        # Scripts
        "scripts",
        "scripts/data_collection",
        "scripts/model_training",
        "scripts/deployment",
        
        # Logs
        "logs",
        "logs/scrapers",
        "logs/models",
        "logs/system",
    ]
    
    # Create all directories
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    # Create __init__.py files for Python packages
    python_packages = [
        "src",
        "src/benter_system",
        "src/benter_system/core",
        "src/benter_system/models",
        "src/benter_system/features",
        "src/benter_system/betting",
        "src/benter_system/risk",
        "src/benter_system/backtesting",
        "src/data_collection",
        "src/data_collection/scrapers",
        "src/data_collection/apis",
        "src/data_collection/validators",
        "src/data_collection/orchestration",
        "src/database",
        "src/database/models",
        "src/database/etl",
        "src/octave",
        "src/octave/compression",
        "src/octave/documentation",
        "src/octave/artifacts",
        "src/semantic_immune",
        "src/semantic_immune/archetypes",
        "src/semantic_immune/antibodies",
        "src/semantic_immune/validators",
        "tests",
        "tests/unit",
        "tests/integration",
    ]
    
    for package in python_packages:
        init_file = Path(package) / "__init__.py"
        init_file.touch()
        print(f"✓ Created __init__.py: {init_file}")

def create_core_config_files():
    """Create essential configuration files"""
    
    # .env template
    env_template = """# Enhanced Benter System Environment Variables
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/benter_system
REDIS_URL=redis://localhost:6379/0

# API Keys (Replace with actual keys)
HK_JOCKEY_CLUB_API_KEY=your_api_key_here
WEATHER_API_KEY=your_weather_api_key
BETTING_EXCHANGE_API_KEY=your_betting_api_key

# Cloud Configuration
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_REGION=us-east-1

# OCTAVE Configuration
OCTAVE_COMPRESSION_LEVEL=3
OCTAVE_SEMANTIC_VALIDATION=true

# Semantic Immune System
MYTHOLOGICAL_PROTECTION_LEVEL=high
DRIFT_DETECTION_THRESHOLD=0.05

# System Configuration
LOG_LEVEL=INFO
DEBUG_MODE=false
ENVIRONMENT=development
"""
    
    with open(".env.template", "w") as f:
        f.write(env_template)
    print("✓ Created .env.template")
    
    # .gitignore
    gitignore_content = """# Enhanced Benter System .gitignore

# Environment and secrets
.env
.env.local
.env.production
*.key
*.pem
secrets/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# Data files
data/raw/*
data/processed/*
!data/raw/.gitkeep
!data/processed/.gitkeep
*.csv
*.parquet
*.h5
*.hdf5

# Model files
models/*.pkl
models/*.joblib
models/*.h5
models/*.pb

# Logs
logs/*
!logs/.gitkeep
*.log

# Jupyter Notebook
.ipynb_checkpoints

# Database
*.db
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp

# Backup files
*.bak
*.backup
"""
    
    with open(".gitignore", "w") as f:
        f.write(gitignore_content)
    print("✓ Created .gitignore")

def create_placeholder_files():
    """Create placeholder files with basic structure"""
    
    # Create .gitkeep files for empty directories
    empty_dirs = [
        "data/raw",
        "data/processed",
        "data/features",
        "data/models",
        "data/backups",
        "logs",
        "logs/scrapers",
        "logs/models",
        "logs/system",
    ]
    
    for directory in empty_dirs:
        gitkeep_file = Path(directory) / ".gitkeep"
        gitkeep_file.touch()
        print(f"✓ Created .gitkeep: {gitkeep_file}")

if __name__ == "__main__":
    print("🏇 Setting up Enhanced Benter Horse Racing System...")
    print("=" * 60)
    
    try:
        create_directory_structure()
        print("\n" + "=" * 60)
        create_core_config_files()
        print("\n" + "=" * 60)
        create_placeholder_files()
        print("\n" + "=" * 60)
        print("✅ Project setup completed successfully!")
        print("\nNext steps:")
        print("1. Activate virtual environment: source venv/bin/activate")
        print("2. Install dependencies: pip install -r enhanced_requirements.txt")
        print("3. Copy .env.template to .env and configure your settings")
        print("4. Begin implementing Phase 1 components")
        
    except Exception as e:
        print(f"❌ Error during setup: {e}")
        sys.exit(1)
