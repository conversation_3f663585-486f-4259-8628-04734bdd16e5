===OCTAVE_MICRO_PRIMER===
// Essential OCTAVE v2.0 in 5 rules (110-140 tokens)

META:
  NAME::"OCTAVE Micro Primer"
  VERSION::"1.1"
  PURPOSE::"Minimal viable OCTAVE for direct model emission"

SEMANTIC_MODE::"Use Greek mythology for compression (domains/patterns/forces)"

RULE_OF_FIVE:
  1_ASSIGNMENT::"KEY::VALUE uses double colon"
  2_HIERARCHY::"Indent exactly 2 spaces per level"
  3_LISTS::"[item1, item2, item3] no trailing comma"
  4_OPERATORS:
    PROGRESSION::"[A->B->C] shows sequence (lists only)"
    SYNTHESIS::"A+B combines elements"
    TENSION::"A _VERSUS_ B shows opposition"
  5_STRUCTURE::"Start with ===NAME===, end with ===END==="

TYPES:
  STRING::bare_word or "with spaces"
  NUMBER::42, 3.14, -1e10
  BOOLEAN::true, false (lowercase only)
  NULL::null (lowercase only)

EXAMPLE:
  STATUS::DEGRADED
  PATTERN::ICARIAN_TRAJECTORY
  FLOW::[INIT->BUILD->DEPLOY]
  TENSION::SPEED _VERSUS_ RELIABILITY
  METRICS:
    CPU::94
    MEMORY::82

===END===