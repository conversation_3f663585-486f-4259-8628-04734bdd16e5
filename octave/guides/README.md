# OCTAVE Guides

This directory contains comprehensive documentation for **OCTAVE** (Olympian Common Text And Vocabulary Engine) - a structured markup language designed for high-density, semantic information representation optimized for LLM processing.

## Quick Start

For immediate use:
- **[octave-quick-start.md](octave-quick-start.md)** - Essential syntax and concepts
- **[octave-micro-primer.oct.md](octave-micro-primer.oct.md)** - Minimal working knowledge

## Core Documentation

### Syntax & Rules
- **[llm-octave-quick-reference.oct.md](llm-octave-quick-reference.oct.md)** - Complete syntax reference
- **[llm-octave-compression-rules.oct.md](llm-octave-compression-rules.oct.md)** - Semantic compression guidelines
- **[octave-literacy-patterns.oct.md](octave-literacy-patterns.oct.md)** - Universal literacy patterns

### Authoring & Mastery
- **[llm-octave-authoring-guide.oct.md](llm-octave-authoring-guide.oct.md)** - Best practices and principles
- **[octave-philosophy.md](octave-philosophy.md)** - Design philosophy and effective patterns
- **[octave-mastery.oct.md](octave-mastery.oct.md)** - Advanced techniques and expertise

### Examples & Patterns
- **[octave-canonical-examples.oct.md](octave-canonical-examples.oct.md)** - Standard format examples

## Key Features

**Semantic Compression**: Uses Greek mythology and symbolic operators for high-density meaning
**LLM Optimized**: Structured for optimal AI processing and understanding  
**Hierarchical**: Clear organizational patterns with precise indentation rules
**Relational**: Emphasizes connections and dependencies between elements

## Core Syntax

```octave
KEY::VALUE          // Assignment with double colon
KEY:                 // Block creation
  CHILD::VALUE       // 2-space indentation
[A->B->C]           // Progression operator
A+B                 // Synthesis operator
A _VERSUS_ B        // Tension operator
===SECTION===       // Major section markers
```

## Philosophy

OCTAVE follows the **Golden Rule**: "If your OCTAVE document were a database schema, would it have foreign keys? If not, you've written a list, not a system."

The focus is on creating **networks of relationships** rather than flat lists, enabling deeper understanding and more effective LLM processing.

## Getting Started

1. Read **octave-quick-start.md** for immediate orientation
2. Review **octave-philosophy.md** for effective patterns
3. Study **octave-canonical-examples.oct.md** for concrete implementations
4. Use **llm-octave-authoring-guide.oct.md** for best practices
5. Reference **OCTAVE_MASTERY.oct.md** for advanced techniques