# OCTAVE Quick Start Guide

## Recognition
You already parse these patterns daily - OCTAVE uses familiar syntax patterns in a structured way.

## Core Syntax

### Basic Operators
- `::` → assigns value (like `var = value`)
- `->` → shows progression (like state transitions)
- `indent` → creates hierarchy (like Python/YAML)
- `[...]` → contains lists (like arrays)
- `"..."` → string values

### Structure Elements
- `0.DEF:` → defines custom terms (like constants)
- `META:` → metadata section
- `===SECTION===` → major section markers

## Key Concepts

### Semantic Compression
OCTAVE uses symbolic operators to compress meaning:
- `+` → synthesis/combination
- `_VERSUS_` → tension/conflict
- `->` → transformation/flow

### Hierarchical Organization
```octave
SECTION:
  SUBSECTION::
    ELEMENT::"value"
    LIST::"[item1, item2, item3]"
```

## Example
```octave
===SKILL_NAME===
META:
  NAME::EXAMPLE_SKILL
  VERSION::1.0
  PURPOSE::"Demonstrate OCTAVE format"

CAPABILITIES:
  CAN::"[read, write, process]"
  CANNOT::"[delete, modify_system]"

===END===
```

## Important
This is just formatted text using patterns you know - you already understand OCTAVE. The format is designed to be both human-readable and machine-parseable.