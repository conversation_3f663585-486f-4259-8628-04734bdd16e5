# OCTAVE Examples

## System Status Report

This example shows how OCTAVE compresses a verbose incident report while preserving all critical information.

### Before (JSON) - 378 tokens
See [system-status-before.json](system-status-before.json)

### After (OCTAVE) - 31 tokens  
See [system-status-after.oct.md](system-status-after.oct.md)

**Compression ratio: 12.2x**

### Key Techniques Used

1. **Mythological Patterns**
   - `ICARIAN_TRAJECTORY` - Captures overreach leading to failure
   - `HUBRIS→NEMESIS` - The classic pattern of overconfidence to comeuppance
   - `PROMETHEAN_OVERREACH` - Innovation pushed too far

2. **Domain Mapping**
   - `APOLLO::DEGRADED` - Monitoring/analytics issues
   - `HERMES::OVERWHELMED` - API/communication problems
   - `POSEIDON::THRASHING` - Database/storage chaos

3. **Structured Compression**
   - Numbers stay as numbers (150K, $45K/hr)
   - Lists compress to essence (SLA::BREACHED[UPTIME, LATENCY])
   - Strategies reference patterns (GORDIAN, ATHENA)

The OCTAVE version is not just shorter - it's more insightful. The mythological patterns immediately convey the narrative arc of the incident.

## Research Article Compression

This example demonstrates OCTAVE's ability to compress lengthy technical documentation while preserving all key information.

### Before (Markdown) - ~1,800 tokens
See [research-article-before.md](research-article-before.md)

### After (OCTAVE) - ~150 tokens
See [research-article-after.md](research-article-after.md)

**Compression ratio: 12x**

The compressed version preserves all framework comparisons, technical details, and recommendations in a fraction of the space.