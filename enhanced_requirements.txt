# ===== ENHANCED BENTER SYSTEM REQUIREMENTS =====
# Core ML and Data Science Stack
numpy==1.25.2
pandas==2.1.3
scipy==1.11.4
scikit-learn==1.3.2
statsmodels==0.14.0

# Advanced ML Libraries
xgboost==2.0.2
lightgbm==4.1.0
catboost==1.2.2
tensorflow==2.15.0
torch==2.1.2
transformers==4.36.2

# Statistical Analysis
pymc==5.10.0
arviz==0.17.0
bayesian-optimization==1.4.3

# Core web scraping (Enhanced)
requests==2.31.0
beautifulsoup4==4.12.2
selenium==4.15.2
lxml==4.9.3
scrapy==2.11.0
aiohttp==3.9.1

# Data processing (Enhanced)
polars==0.20.2
dask==2023.12.1
modin==0.25.0

# Database (Enhanced)
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.1
redis==5.0.1
pymongo==4.6.1

# Time Series Analysis
prophet==1.1.5
arch==6.2.0
tsfresh==0.20.1

# Financial Analysis
yfinance==0.2.28
quantlib==1.32
zipline-reloaded==3.0.4

# Optimization
cvxpy==1.4.1
pulp==2.7.0
optuna==3.5.0

# Utilities (Enhanced)
python-dotenv==1.0.0
schedule==1.2.0
click==8.1.7
rich==13.7.0
typer==0.9.0
pydantic==2.5.2

# Development Tools
jupyter==1.0.0
jupyterlab==4.0.9
ipywidgets==8.1.1
notebook==7.0.6

# Testing
pytest==7.4.3
pytest-cov==4.1.0
pytest-mock==3.12.0

# Code Quality
black==23.12.0
isort==5.13.2
flake8==6.1.0
mypy==1.8.0

# Monitoring and Logging
loguru==0.7.2
prometheus-client==0.19.0
grafana-api==1.0.3

# Cloud and Infrastructure
boto3==1.34.0
google-cloud-storage==2.10.0
azure-storage-blob==12.19.0
docker==6.1.3

# API Development
fastapi==0.104.1
uvicorn==0.24.0

# Visualization
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
bokeh==3.3.2

# OCTAVE Integration Dependencies
markdown==3.5.1
pyyaml==6.0.1
jinja2==3.1.2

# Semantic Processing
spacy==3.7.2
nltk==3.8.1
gensim==4.3.2

# Configuration Management
hydra-core==1.3.2
omegaconf==2.3.0

# Parallel Processing
joblib==1.3.2
multiprocessing-logging==0.3.4

# Memory Optimization
memory-profiler==0.61.0
psutil==5.9.6

# Networking
httpx==0.25.2
websockets==12.0

# Cryptography (for secure API keys)
cryptography==41.0.8
keyring==24.3.0

# Kelly Criterion and Betting Optimization
kelly-criterion==1.0.0
scipy-optimize==1.11.4

# Horse Racing Specific
racing-data==0.1.0
equibase-parser==0.2.0

# Weather Data
openweathermap==0.1.0
weather-api==1.0.0

# Market Data
ccxt==4.1.0
alpha-vantage==2.3.1

# Semantic Immune System Dependencies
mythological-types==1.0.0
archetype-validator==0.1.0
semantic-drift-detector==0.2.0

# OCTAVE Compression
octave-semantic==1.0.0
knowledge-compressor==0.1.0
repomix-integration==0.2.0
