# Enhanced Benter System .gitignore

# Environment and secrets
.env
.env.local
.env.production
*.key
*.pem
secrets/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# Data files
data/raw/*
data/processed/*
!data/raw/.gitkeep
!data/processed/.gitkeep
*.csv
*.parquet
*.h5
*.hdf5

# Model files
models/*.pkl
models/*.joblib
models/*.h5
models/*.pb

# Logs
logs/*
!logs/.gitkeep
*.log

# Jupyter Notebook
.ipynb_checkpoints

# Database
*.db
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp

# Backup files
*.bak
*.backup
