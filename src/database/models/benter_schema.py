"""
Enhanced Benter System Database Schema
Comprehensive PostgreSQL schema for horse racing data with semantic validation
"""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Boolean, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, ARRAY
import uuid
from datetime import datetime

Base = declarative_base()

class Track(Base):
    """Racing tracks/venues"""
    __tablename__ = 'tracks'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, unique=True)
    country = Column(String(50), nullable=False)
    surface_types = Column(ARRAY(String), nullable=False)  # ['turf', 'dirt', 'synthetic']
    distances = Column(ARRAY(Integer), nullable=False)  # Available distances
    timezone = Column(String(50), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Semantic validation
    semantic_signature = Column(JSON)
    
    # Relationships
    races = relationship("Race", back_populates="track")

class Horse(Base):
    """Horse information with enhanced tracking"""
    __tablename__ = 'horses'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    country_of_birth = Column(String(50))
    foaling_date = Column(DateTime)
    sex = Column(String(10))  # 'M', 'F', 'G', 'C'
    color = Column(String(50))
    sire = Column(String(100))
    dam = Column(String(100))
    trainer_id = Column(UUID(as_uuid=True), ForeignKey('trainers.id'))
    owner = Column(String(200))
    
    # Enhanced tracking
    current_rating = Column(Float)
    peak_rating = Column(Float)
    form_cycle = Column(String(20))  # 'improving', 'declining', 'stable', 'peak'
    last_equipment_change = Column(DateTime)
    retirement_date = Column(DateTime)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Semantic validation
    semantic_signature = Column(JSON)
    
    # Relationships
    trainer = relationship("Trainer", back_populates="horses")
    race_entries = relationship("RaceEntry", back_populates="horse")
    
    __table_args__ = (
        Index('idx_horse_name', 'name'),
        Index('idx_horse_trainer', 'trainer_id'),
    )

class Jockey(Base):
    """Jockey information with performance tracking"""
    __tablename__ = 'jockeys'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    license_number = Column(String(50), unique=True)
    country = Column(String(50))
    weight_allowance = Column(Float)  # Apprentice allowance
    
    # Performance metrics
    career_wins = Column(Integer, default=0)
    career_starts = Column(Integer, default=0)
    current_season_wins = Column(Integer, default=0)
    current_season_starts = Column(Integer, default=0)
    win_percentage = Column(Float)
    
    # Enhanced metrics
    track_specialties = Column(ARRAY(String))  # Tracks where jockey excels
    distance_preferences = Column(JSON)  # Distance performance breakdown
    surface_performance = Column(JSON)  # Performance by surface
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Semantic validation
    semantic_signature = Column(JSON)
    
    # Relationships
    race_entries = relationship("RaceEntry", back_populates="jockey")
    
    __table_args__ = (
        Index('idx_jockey_name', 'name'),
        CheckConstraint('win_percentage >= 0 AND win_percentage <= 1'),
    )

class Trainer(Base):
    """Trainer information with performance tracking"""
    __tablename__ = 'trainers'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    license_number = Column(String(50), unique=True)
    stable_location = Column(String(200))
    
    # Performance metrics
    career_wins = Column(Integer, default=0)
    career_starts = Column(Integer, default=0)
    current_season_wins = Column(Integer, default=0)
    current_season_starts = Column(Integer, default=0)
    win_percentage = Column(Float)
    
    # Enhanced metrics
    specialties = Column(ARRAY(String))  # Training specialties
    class_performance = Column(JSON)  # Performance by class level
    distance_performance = Column(JSON)  # Performance by distance
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Semantic validation
    semantic_signature = Column(JSON)
    
    # Relationships
    horses = relationship("Horse", back_populates="trainer")
    race_entries = relationship("RaceEntry", back_populates="trainer")
    
    __table_args__ = (
        Index('idx_trainer_name', 'name'),
        CheckConstraint('win_percentage >= 0 AND win_percentage <= 1'),
    )

class Race(Base):
    """Race information with enhanced data points"""
    __tablename__ = 'races'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    track_id = Column(UUID(as_uuid=True), ForeignKey('tracks.id'), nullable=False)
    race_date = Column(DateTime, nullable=False)
    race_number = Column(Integer, nullable=False)
    race_name = Column(String(200))
    
    # Race conditions
    distance = Column(Integer, nullable=False)  # Distance in meters
    surface = Column(String(20), nullable=False)  # 'turf', 'dirt', 'synthetic'
    class_level = Column(String(50))
    purse = Column(Float)
    conditions = Column(Text)
    
    # Track conditions
    going = Column(String(20))  # 'firm', 'good', 'soft', 'heavy'
    weather = Column(String(50))
    temperature = Column(Float)
    humidity = Column(Float)
    wind_speed = Column(Float)
    wind_direction = Column(String(10))
    
    # Enhanced data
    track_bias = Column(String(50))  # 'speed_favoring', 'closer_favoring', 'neutral'
    pace_scenario = Column(String(50))  # 'fast', 'moderate', 'slow'
    field_size = Column(Integer)
    
    # Sectional times and pace analysis
    sectional_times = Column(ARRAY(Float))
    pace_figures = Column(JSON)  # Early, mid, late pace figures
    
    # Results
    winning_time = Column(Float)
    winning_margin = Column(Float)
    race_rating = Column(Float)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Semantic validation
    semantic_signature = Column(JSON)
    
    # Relationships
    track = relationship("Track", back_populates="races")
    entries = relationship("RaceEntry", back_populates="race")
    
    __table_args__ = (
        Index('idx_race_date', 'race_date'),
        Index('idx_race_track_date', 'track_id', 'race_date'),
        UniqueConstraint('track_id', 'race_date', 'race_number'),
        CheckConstraint('distance > 0'),
        CheckConstraint('race_number > 0'),
    )

class RaceEntry(Base):
    """Individual horse entries in races with comprehensive data"""
    __tablename__ = 'race_entries'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    race_id = Column(UUID(as_uuid=True), ForeignKey('races.id'), nullable=False)
    horse_id = Column(UUID(as_uuid=True), ForeignKey('horses.id'), nullable=False)
    jockey_id = Column(UUID(as_uuid=True), ForeignKey('jockeys.id'), nullable=False)
    trainer_id = Column(UUID(as_uuid=True), ForeignKey('trainers.id'), nullable=False)
    
    # Entry details
    post_position = Column(Integer, nullable=False)
    program_number = Column(String(10))
    weight_carried = Column(Float, nullable=False)
    weight_allowance = Column(Float, default=0)
    
    # Equipment
    equipment = Column(JSON)  # blinkers, tongue_tie, etc.
    equipment_changes = Column(ARRAY(String))
    
    # Pre-race data
    morning_line_odds = Column(Float)
    final_odds = Column(Float)
    public_choice = Column(Integer)  # 1=favorite, 2=second choice, etc.
    
    # Performance data
    finishing_position = Column(Integer)
    lengths_behind = Column(Float)
    individual_time = Column(Float)
    speed_figure = Column(Float)
    pace_figure = Column(Float)
    class_rating = Column(Float)
    
    # Enhanced performance metrics
    sectional_positions = Column(ARRAY(Integer))  # Position at each sectional
    running_style = Column(String(20))  # 'front_runner', 'stalker', 'closer'
    trip_notes = Column(Text)
    trouble_indicators = Column(ARRAY(String))
    
    # Betting data
    win_payout = Column(Float)
    place_payout = Column(Float)
    show_payout = Column(Float)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Semantic validation
    semantic_signature = Column(JSON)
    
    # Relationships
    race = relationship("Race", back_populates="entries")
    horse = relationship("Horse", back_populates="race_entries")
    jockey = relationship("Jockey", back_populates="race_entries")
    trainer = relationship("Trainer", back_populates="race_entries")
    
    __table_args__ = (
        Index('idx_entry_race', 'race_id'),
        Index('idx_entry_horse', 'horse_id'),
        Index('idx_entry_jockey', 'jockey_id'),
        Index('idx_entry_trainer', 'trainer_id'),
        UniqueConstraint('race_id', 'post_position'),
        CheckConstraint('post_position > 0'),
        CheckConstraint('weight_carried > 0'),
        CheckConstraint('finishing_position > 0 OR finishing_position IS NULL'),
    )
