Abstract

This paper presents a comprehensive analysis of computer-based horse race handicapping and wagering systems. The research demonstrates that significant profits can be achieved through systematic application of statistical modeling and optimal betting strategies in horse racing markets.

Key Mathematical Models
Multinomial Logit Model
P(horse i wins) = exp(X_i * β) / Σ(exp(X_j * β)) for all horses j

Where X_i represents the feature vector for horse i, and β represents the coefficient vector.

Kelly Criterion for Optimal Betting
f* = (bp - q) / b Where: f* = fraction of bankroll to wager b = odds received (decimal odds - 1) p = probability of winning (from model) q = probability of losing (1 - p)
Feature Variables Used
Category	Variable	Description
Horse Performance	Recent Form	Win/place percentage in last N races
Horse Performance	Speed Ratings	Normalized speed figures
Race Conditions	Distance	Race distance in furlongs
Race Conditions	Track Condition	Fast, good, yielding, soft, heavy
Jockey/Trainer	Win Percentage	Success rate for jockey-trainer combination
Market Data	Starting Price	Final odds at race start
Derived	Class Rating	Competitive level adjustment
Model Performance Results
Metric	Value	Notes
Win Prediction Accuracy	~32%	Compared to ~17% random chance
ROI on Win Bets	+8.5%	After 20% track takeout
Sharpe Ratio	1.73	Risk-adjusted returns
Maximum Drawdown	-15.2%	Largest peak-to-trough decline
Key Implementation Insights
Feature Engineering: Interaction terms between variables (e.g., jockey-trainer combinations) proved crucial
Model Stability: Regular retraining required due to changing conditions and participants
Betting Strategy: Conservative Kelly fractions (0.25 * Kelly) used to reduce volatility
Market Efficiency: Win pools showed less efficiency than exotic betting pools
Data Quality: Consistent, comprehensive data collection was critical for success
Computational Requirements: Real-time processing needed for odds movement analysis
Statistical Significance Tests
t-statistic = (observed_return - expected_return) / (standard_error) Where standard_error = σ / √n σ = standard deviation of returns n = number of betting opportunities

Results showed statistical significance at p < 0.01 level over sample periods of 6+ months.