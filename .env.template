# Enhanced Benter System Environment Variables
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/benter_system
REDIS_URL=redis://localhost:6379/0

# API Keys (Replace with actual keys)
HK_JOCKEY_CLUB_API_KEY=your_api_key_here
WEATHER_API_KEY=your_weather_api_key
BETTING_EXCHANGE_API_KEY=your_betting_api_key

# Cloud Configuration
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_REGION=us-east-1

# OCTAVE Configuration
OCTAVE_COMPRESSION_LEVEL=3
OCTAVE_SEMANTIC_VALIDATION=true

# Semantic Immune System
MYTHOLOGICAL_PROTECTION_LEVEL=high
DRIFT_DETECTION_THRESHOLD=0.05

# System Configuration
LOG_LEVEL=INFO
DEBUG_MODE=false
ENVIRONMENT=development
